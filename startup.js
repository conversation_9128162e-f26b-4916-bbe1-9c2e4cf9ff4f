const express = require('express');
const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');
const cors = require('cors');
const https = require('https');

const app = express();
app.use(cors());
app.use(express.json({ limit: '50mb' }));

let devServerProcess = null;

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    devServer: !!devServerProcess,
    timestamp: new Date().toISOString(),
  });
});

// Code injection endpoint
app.post('/inject-code', async (req, res) => {
  try {
    const { files } = req.body;

    if (!files || typeof files !== 'object') {
      return res.status(400).json({ error: 'Files object required' });
    }

    console.log(`Injecting ${Object.keys(files).length} files...`);

    // Kill existing dev server
    if (devServerProcess) {
      devServerProcess.kill('SIGTERM');
      devServerProcess = null;
    } else {
      // If devServerProcess is not set, try to kill using the PID file
      try {
        const pid = fs.readFileSync('/tmp/dev-server.pid', 'utf8').trim();
        if (pid) {
          process.kill(parseInt(pid), 'SIGTERM');
          console.log(`Killed existing dev server with PID ${pid}`);
        }
      } catch (error) {
        console.log('No existing dev server PID found or could not kill it');
      }
    }

    // Write all files
    for (const [filePath, content] of Object.entries(files)) {
      const fullPath = path.join('/app', filePath);
      await fs.ensureDir(path.dirname(fullPath));
      await fs.writeFile(fullPath, content || '');
    }

    console.log('Files written successfully');

    // Start dev server
    devServerProcess = spawn('npm', ['run', 'dev'], {
      cwd: '/app',
      stdio: 'pipe',
      env: { ...process.env, PORT: '3000' },
    });

    devServerProcess.stdout.on('data', (data) => {
      console.log(`Dev server: ${data}`);
    });

    devServerProcess.stderr.on('data', (data) => {
      console.error(`Dev server error: ${data}`);
    });

    devServerProcess.on('close', (code) => {
      console.log(`Dev server exited with code ${code}`);
      devServerProcess = null;
    });

    // Wait a moment for server to start
    setTimeout(() => {
      res.json({
        success: true,
        message: 'Code injected and dev server started',
        filesCount: Object.keys(files).length,
      });
    }, 2000);
  } catch (error) {
    console.error('Code injection failed:', error);
    res.status(500).json({
      error: 'Code injection failed',
      details: error.message,
    });
  }
});

// Start code injection server
const PORT = process.env.PORT || 8080;

// Check if we should use HTTPS (for production deployments)
if (process.env.NODE_ENV === 'production' || process.env.USE_HTTPS === 'true') {
   // For production, we'll rely on the platform's SSL termination
   // and just serve HTTP internally
   app.listen(PORT, () => {
      console.log(`Code injection API listening on port ${PORT} (HTTP behind SSL proxy)`);
      console.log('Waiting for code injection...');
   });
} else {
   // For local development
   app.listen(PORT, () => {
      console.log(`Code injection API listening on port ${PORT} (HTTP)`);
      console.log('Waiting for code injection...');
   });
}
