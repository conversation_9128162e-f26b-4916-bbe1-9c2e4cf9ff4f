FROM node:18-alpine

WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm@latest

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies with pnpm
RUN pnpm install

# Copy all project files
COPY . .

# Expose ports (3000 for Next.js, 8080 for code injection server)
EXPOSE 3000 8080

# Make scripts executable
RUN chmod +x startup.js
RUN chmod +x start.sh

# Use the shell script as the entry point
CMD ["/app/start.sh"]
