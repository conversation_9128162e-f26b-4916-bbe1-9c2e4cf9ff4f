'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
   Card,
   CardContent,
   CardDescription,
   CardFooter,
   CardHeader,
   CardTitle,
} from '@/components/ui/card';
import { AlertTriangleIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function ErrorPage() {
   const router = useRouter();

   return (
      <div className='flex min-h-screen flex-col items-center justify-center p-4'>
         <Card className='w-full max-w-md shadow-lg'>
            <CardHeader className='space-y-1'>
               <div className='flex items-center justify-center mb-2'>
                  <AlertTriangleIcon className='h-10 w-10 text-destructive' />
               </div>
               <CardTitle className='text-2xl font-bold text-center'>
                  Something went wrong
               </CardTitle>
               <CardDescription className='text-center'>
                  We encountered an error while processing your request.
               </CardDescription>
            </CardHeader>
            <CardContent>
               <p className='text-center text-muted-foreground'>
                  This could be due to an authentication issue or a server error. Please try again
                  or contact support if the problem persists.
               </p>
            </CardContent>
            <CardFooter className='flex justify-center'>
               <Button onClick={() => router.push('/auth/login')} className='w-full'>
                  Return to Login
               </Button>
            </CardFooter>
         </Card>
      </div>
   );
}
