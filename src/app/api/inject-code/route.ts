import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs-extra';
import path from 'path';

export async function POST(request: NextRequest) {
  // Only allow in development environment
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json(
      { error: 'Code injection is only allowed in development mode' },
      { status: 403 }
    );
  }

  try {
    const body = await request.json();
    const { files } = body;

    if (!files || typeof files !== 'object') {
      return NextResponse.json(
        { error: 'Files object required' },
        { status: 400 }
      );
    }

    console.log(`Injecting ${Object.keys(files).length} files...`);

    // Get the project root directory
    const projectRoot = process.cwd();

    // Write all files
    for (const [filePath, content] of Object.entries(files)) {
      const fullPath = path.join(projectRoot, filePath);
      await fs.ensureDir(path.dirname(fullPath));
      await fs.writeFile(fullPath, content || '');
    }

    console.log('Files written successfully');

    // For development mode, we don't need to restart the server
    // The files are already written and Next.js will hot-reload automatically
    console.log('Files injected successfully. Next.js will hot-reload automatically.');

    // Just wait a moment for file system to settle
    await new Promise(resolve => setTimeout(resolve, 500));

    return NextResponse.json({
      success: true,
      message: 'Code injected successfully. Next.js will hot-reload automatically.',
      filesCount: Object.keys(files).length,
    });

  } catch (error) {
    console.error('Code injection failed:', error);
    return NextResponse.json(
      {
        error: 'Code injection failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
  });
}
